/**
 * API Client for Project Management System
 * Centralized HTTP client for all backend communication
 */

/// <reference types="vite/client" />

import { supabase } from '../services/supabaseClient';
// Import types from existing type files
import type {
  Tarea,
  TareaCreate,
  TareaUpdate,
  TareaListResponse,
  TareaMatrix,
  TareaKanbanBoard,
} from '../types/tarea';

import type {
  DashboardData,
  QuickActionsResponse,
  DashboardNotificationsResponse,
} from '../types/dashboard';

import type {
  Proyecto,
  ProyectoCreate,
  ProyectoUpdate,
  ProyectoListResponse,
} from '../types/proyecto';

import type {
  Proceso,
  ProcesoCreate,
  ProcesoUpdate,
  ProcesoListResponse,
} from '../types/proceso';

import type {
  GlobalSearchResponse,
} from '../types/search';

import type {
  Empresa,
  EmpresaCreate,
  EmpresaUpdate,
  EmpresaStats,
  EmpresaGeneralDetails,
  EmpresaHallazgosDetails,
  HallazgoDetail,
} from '../types/empresa';

// Define API-specific types inline
export type ApiParams = Record<string, string | number | boolean | undefined | null>;
export type ApiRequestBody = Record<string, unknown> | FormData | string | null | undefined | object;

export interface ProyectoOption {
  id: string;
  nombre: string;
}

export interface EmpresaOption {
  id: string;
  nombre: string;
}

export interface HealthResponse {
  status: string;
  timestamp: string;
}

// Get API URL from environment variables with debugging
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Debug environment variables (always show in console for troubleshooting)
console.log('🔧 API Configuration Debug:');
console.log('VITE_API_URL:', import.meta.env.VITE_API_URL);
console.log('VITE_ENVIRONMENT:', import.meta.env.VITE_ENVIRONMENT);
console.log('API_BASE_URL:', API_BASE_URL);
console.log('Mode:', import.meta.env.MODE);
console.log('DEV:', import.meta.env.DEV);
console.log('PROD:', import.meta.env.PROD);

// Warn if using localhost in production
if (import.meta.env.PROD && API_BASE_URL.includes('localhost')) {
  console.error('⚠️ WARNING: Using localhost API URL in production!');
  console.error('Check Coolify Build Arguments configuration');
}

/**
 * HTTP Client with automatic error handling and authentication
 */
class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Set authentication token for all requests
   */
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Remove authentication token
   */
  clearAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Generic HTTP request method
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // Get current session token from Supabase
    const { data: { session } } = await supabase.auth.getSession();
    const headers = { ...this.defaultHeaders };

    // Add authorization header if user is authenticated
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      // Handle non-JSON responses (like 204 No Content)
      if (response.status === 204) {
        return {} as T;
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API Error [${options.method || 'GET'}] ${url}:`, error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: ApiParams): Promise<T> {
    let url = endpoint;

    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });

      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }
    }

    return this.request<T>(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: ApiRequestBody): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: ApiRequestBody): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: ApiRequestBody): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Create API client instance
const api = new ApiClient(API_BASE_URL);

/**
 * Organized API endpoints by feature
 */
export const apiClient = {
  // Dashboard endpoints
  dashboard: {
    getData: () => api.get<DashboardData>('/api/v1/dashboard'),
    getQuickActions: () => api.get<QuickActionsResponse>('/api/v1/dashboard/quick-actions'),
    getNotifications: () => api.get<DashboardNotificationsResponse>('/api/v1/dashboard/notifications'),
  },

  // Projects endpoints
  proyectos: {
    getAll: (params?: ApiParams) => api.get<ProyectoListResponse>('/api/v1/proyectos', params),
    getById: (id: string) => api.get<Proyecto>(`/api/v1/proyectos/${id}`),
    create: (data: ProyectoCreate) => api.post<Proyecto>('/api/v1/proyectos', data),
    update: (id: string, data: ProyectoUpdate) => api.put<Proyecto>(`/api/v1/proyectos/${id}`, data),
    delete: (id: string) => api.delete<void>(`/api/v1/proyectos/${id}`),
    getEstados: () => api.get<{ estados: string[] }>('/api/v1/proyectos/estados/available'),
    getPrioridades: () => api.get<{ prioridades: string[] }>('/api/v1/proyectos/prioridades/available'),
  },

  // Processes endpoints
  procesos: {
    getAll: (params?: ApiParams) => api.get<ProcesoListResponse>('/api/v1/procesos', params),
    getById: (id: string) => api.get<Proceso>(`/api/v1/procesos/${id}`),
    create: (data: ProcesoCreate) => api.post<Proceso>('/api/v1/procesos', data),
    update: (id: string, data: ProcesoUpdate) => api.put<Proceso>(`/api/v1/procesos/${id}`, data),
    delete: (id: string) => api.delete<void>(`/api/v1/procesos/${id}`),
    getTipos: () => api.get<{ tipos: string[] }>('/api/v1/procesos/tipos/available'),
    getEstados: () => api.get<{ estados: string[] }>('/api/v1/procesos/estados/available'),
  },

  // Tasks endpoints
  tareas: {
    getAll: (params?: ApiParams) => api.get<TareaListResponse>('/api/v1/tareas', params),
    getById: (id: string) => api.get<Tarea>(`/api/v1/tareas/${id}`),
    create: (data: TareaCreate) => api.post<Tarea>('/api/v1/tareas', data),
    update: (id: string, data: TareaUpdate) => api.put<Tarea>(`/api/v1/tareas/${id}`, data),
    delete: (id: string) => api.delete<void>(`/api/v1/tareas/${id}`),
    getMatrix: (params?: ApiParams) => api.get<TareaMatrix>('/api/v1/tareas/matrix', params),
    getKanban: (params?: ApiParams) => api.get<TareaKanbanBoard>('/api/v1/tareas/kanban', params),
    getEstados: () => api.get<{ estados: string[] }>('/api/v1/tareas/estados/available'),
    getPrioridades: () => api.get<{ prioridades: string[] }>('/api/v1/tareas/prioridades/available'),
    getUrgencias: () => api.get<{ urgencias: string[] }>('/api/v1/tareas/urgencias/available'),
    getProyectos: () => api.get<ProyectoOption[]>('/api/v1/tareas/proyectos/available'),
    getEmpresas: () => api.get<EmpresaOption[]>('/api/v1/tareas/empresas/available'),
  },

  // Empresas endpoints
  empresas: {
    getAll: (params?: ApiParams) => api.get<Empresa[]>('/api/v1/empresas', params),
    getById: (id: string) => api.get<Empresa>(`/api/v1/empresas/${id}`),
    getGeneralDetails: (id: string) => api.get<EmpresaGeneralDetails>(`/api/v1/empresas/${id}/general-details`),
    getHallazgosDetails: (id: string) => api.get<EmpresaHallazgosDetails>(`/api/v1/empresas/${id}/hallazgos-details`),
    getHallazgoDetail: (hallazgoId: string) => api.get<HallazgoDetail>(`/api/v1/empresas/hallazgos/${hallazgoId}`),
    create: (data: EmpresaCreate) => api.post<Empresa>('/api/v1/empresas', data),
    update: (id: string, data: EmpresaUpdate) => api.put<Empresa>(`/api/v1/empresas/${id}`, data),
    delete: (id: string) => api.delete<void>(`/api/v1/empresas/${id}`),
    getStats: () => api.get<EmpresaStats>('/api/v1/empresas/stats'),
    getTiposRelacion: () => api.get<{ tipos: string[] }>('/api/v1/empresas/tipos-relacion/available'),
  },

  // Search endpoints
  search: {
    global: (query: string, params?: ApiParams) =>
      api.get<GlobalSearchResponse>('/api/v1/search', { q: query, ...params }),
    quick: (query: string, limit?: number) =>
      api.get<GlobalSearchResponse>('/api/v1/search/quick', { q: query, limit }),
    suggestions: (query: string, limit?: number) =>
      api.get<string[]>('/api/v1/search/suggestions', { q: query, limit }),
  },

  // Health check
  health: () => api.get<HealthResponse>('/health'),
};

// Export the raw API client for custom requests
export { api };

// Export auth methods for easy access
export const setAuthToken = (token: string) => api.setAuthToken(token);
export const clearAuthToken = () => api.clearAuthToken();

// Default export
export default apiClient;
