/**
 * PestanaReunionesEmpresa - Reuniones tab component for company details
 * Shows comprehensive meetings information with filtering, timeline, and people panel
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '../../../lib/api';
import LoadingSpinner from '../../UI/LoadingSpinner';
import type { 
  EmpresaReunionesDetails, 
  ReunionListItem, 
  PersonaEstadoEntrevista,
  ReunionesFilterState,
  ReunionSortState,
  ReunionSortField,
  SortDirection
} from '../../../types/empresa';
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Eye,
  Calendar,
  Users,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  ChevronDown,
  ChevronUp,
  Video,
  MessageSquare
} from 'lucide-react';

interface PestanaReunionesEmpresaProps {
  empresaId: string;
}

const PestanaReunionesEmpresa: React.FC<PestanaReunionesEmpresaProps> = ({ empresaId }) => {
  const navigate = useNavigate();
  
  // Data state
  const [data, setData] = useState<EmpresaReunionesDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter and sort state
  const [filters, setFilters] = useState<ReunionesFilterState>({
    tipo: 'todas',
    search: ''
  });
  
  const [sortState, setSortState] = useState<ReunionSortState>({
    field: 'fecha_reunion',
    direction: 'desc'
  });
  
  // UI state
  const [showFilters, setShowFilters] = useState(false);
  const [showPeoplePanel, setShowPeoplePanel] = useState(true);
  const [showTimeline, setShowTimeline] = useState(true);

  useEffect(() => {
    const fetchReunionesDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiClient.empresas.getReunionesDetails(empresaId);
        setData(response);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error loading reuniones details';
        setError(errorMessage);
        console.error('Error fetching empresa reuniones details:', err);
      } finally {
        setLoading(false);
      }
    };

    if (empresaId) {
      fetchReunionesDetails();
    }
  }, [empresaId]);

  // Filter and sort reuniones
  const filteredAndSortedReuniones = useMemo(() => {
    if (!data?.reuniones_list) return [];

    let filtered = data.reuniones_list;

    // Apply filters
    if (filters.tipo && filters.tipo !== 'todas') {
      if (filters.tipo === 'reuniones') {
        filtered = filtered.filter(reunion => reunion.tipo === 'Reunión');
      } else if (filters.tipo === 'entrevistas') {
        filtered = filtered.filter(reunion => reunion.tipo === 'Entrevista');
      }
    }

    if (filters.persona_id) {
      filtered = filtered.filter(reunion => 
        reunion.personas_asistentes_ids.includes(filters.persona_id!)
      );
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(reunion =>
        reunion.titulo?.toLowerCase().includes(searchLower) ||
        reunion.personas_asistentes.some(persona => 
          persona.toLowerCase().includes(searchLower)
        )
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortState.field];
      let bValue: any = b[sortState.field];

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) aValue = '';
      if (bValue === null || bValue === undefined) bValue = '';

      // Convert dates to comparable format
      if (sortState.field === 'fecha_reunion') {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }

      // String comparison for other fields
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortState.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortState.direction === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [data?.reuniones_list, filters, sortState]);

  const handleSortChange = (field: ReunionSortField) => {
    setSortState(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleViewMeeting = (reunionId: string) => {
    navigate(`/meetings/${reunionId}`);
  };

  const clearFilters = () => {
    setFilters({
      tipo: 'todas',
      search: ''
    });
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Sin fecha';
    try {
      return new Date(dateString).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Fecha inválida';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <XCircle className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error al cargar los datos</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No se encontraron datos para esta empresa.</p>
      </div>
    );
  }

  const { reuniones_list, personas_empresa, timeline_items, estadisticas } = data;

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <MessageSquare className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Reuniones</p>
              <p className="text-2xl font-semibold text-gray-900">{estadisticas.total_reuniones}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Video className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Entrevistas</p>
              <p className="text-2xl font-semibold text-gray-900">{estadisticas.total_entrevistas}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Personas Entrevistadas</p>
              <p className="text-2xl font-semibold text-gray-900">{estadisticas.total_personas_entrevistadas}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-orange-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">% Entrevistadas</p>
              <p className="text-2xl font-semibold text-gray-900">{estadisticas.porcentaje_personas_entrevistadas}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Buscar reuniones..."
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Type Filter */}
            <select
              value={filters.tipo || 'todas'}
              onChange={(e) => setFilters(prev => ({ ...prev, tipo: e.target.value as any }))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="todas">Todas</option>
              <option value="reuniones">Solo Reuniones</option>
              <option value="entrevistas">Solo Entrevistas</option>
            </select>

            {/* Person Filter */}
            <select
              value={filters.persona_id || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, persona_id: e.target.value || undefined }))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todas las personas</option>
              {personas_empresa.map(persona => (
                <option key={persona.id} value={persona.id}>
                  {persona.nombre} {persona.apellidos}
                </option>
              ))}
            </select>
          </div>

          {/* Clear Filters */}
          {(filters.search || filters.tipo !== 'todas' || filters.persona_id) && (
            <button
              onClick={clearFilters}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Limpiar filtros
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content - Meetings List */}
        <div className="lg:col-span-2 space-y-6">
          {/* Meetings List */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Lista de Reuniones ({filteredAndSortedReuniones.length})
                </h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleSortChange('fecha_reunion')}
                    className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <Calendar className="h-4 w-4" />
                    <span>Fecha</span>
                    {sortState.field === 'fecha_reunion' && (
                      sortState.direction === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                    )}
                  </button>
                  <button
                    onClick={() => handleSortChange('titulo')}
                    className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <span>Título</span>
                    {sortState.field === 'titulo' && (
                      sortState.direction === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-200">
              {filteredAndSortedReuniones.length === 0 ? (
                <div className="px-6 py-8 text-center">
                  <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No hay reuniones</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {filters.search || filters.tipo !== 'todas' || filters.persona_id
                      ? 'No se encontraron reuniones con los filtros aplicados.'
                      : 'Esta empresa no tiene reuniones registradas.'}
                  </p>
                </div>
              ) : (
                filteredAndSortedReuniones.map((reunion) => (
                  <div key={reunion.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3">
                          <div className={`flex-shrink-0 w-2 h-2 rounded-full ${
                            reunion.tipo === 'Entrevista' ? 'bg-green-500' : 'bg-blue-500'
                          }`} />
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {reunion.titulo || 'Reunión sin título'}
                            </h4>
                            <div className="flex items-center space-x-4 mt-1">
                              <span className="text-xs text-gray-500">
                                {formatDate(reunion.fecha_reunion)}
                              </span>
                              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                reunion.tipo === 'Entrevista'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {reunion.tipo}
                              </span>
                              {reunion.estado_procesamiento && (
                                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                  reunion.estado_procesamiento === 'completado'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {reunion.estado_procesamiento}
                                </span>
                              )}
                            </div>
                            {reunion.personas_asistentes.length > 0 && (
                              <div className="flex items-center mt-2">
                                <Users className="h-4 w-4 text-gray-400 mr-1" />
                                <span className="text-xs text-gray-600">
                                  {reunion.personas_asistentes.join(', ')}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <button
                          onClick={() => handleViewMeeting(reunion.id)}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Ver detalles
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Timeline Section */}
          {showTimeline && timeline_items.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Timeline de Reuniones</h3>
                  <button
                    onClick={() => setShowTimeline(!showTimeline)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {showTimeline ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
                  </button>
                </div>
              </div>
              <div className="px-6 py-4">
                <div className="flow-root">
                  <ul className="-mb-8">
                    {timeline_items.slice(0, 10).map((item, index) => (
                      <li key={item.id}>
                        <div className="relative pb-8">
                          {index !== timeline_items.slice(0, 10).length - 1 && (
                            <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                          )}
                          <div className="relative flex space-x-3">
                            <div>
                              <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                                item.tipo === 'Entrevista' ? 'bg-green-500' : 'bg-blue-500'
                              }`}>
                                {item.tipo === 'Entrevista' ? (
                                  <Video className="h-4 w-4 text-white" />
                                ) : (
                                  <MessageSquare className="h-4 w-4 text-white" />
                                )}
                              </span>
                            </div>
                            <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                              <div>
                                <p className="text-sm text-gray-900 font-medium">
                                  {item.titulo || 'Reunión sin título'}
                                </p>
                                <p className="text-sm text-gray-500">
                                  {item.tipo} • {formatDate(item.fecha_reunion)}
                                </p>
                                {item.personas_asistentes.length > 0 && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    Asistentes: {item.personas_asistentes.join(', ')}
                                  </p>
                                )}
                                {item.resumen_breve && (
                                  <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                    {item.resumen_breve}
                                  </p>
                                )}
                              </div>
                              <div className="text-right text-sm whitespace-nowrap text-gray-500">
                                <button
                                  onClick={() => handleViewMeeting(item.id)}
                                  className="text-blue-600 hover:text-blue-800 text-xs"
                                >
                                  Ver detalles
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar - People Panel */}
        <div className="space-y-6">
          {/* People Panel */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Personas de la Empresa ({personas_empresa.length})
                </h3>
                <button
                  onClick={() => setShowPeoplePanel(!showPeoplePanel)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {showPeoplePanel ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
                </button>
              </div>
            </div>

            {showPeoplePanel && (
              <div className="px-6 py-4">
                {personas_empresa.length === 0 ? (
                  <div className="text-center py-4">
                    <Users className="mx-auto h-8 w-8 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">No hay personas registradas</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {personas_empresa.map((persona) => (
                      <div key={persona.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {persona.nombre} {persona.apellidos}
                            </h4>
                            {persona.entrevistado ? (
                              <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                            ) : (
                              <Clock className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                            )}
                          </div>
                          {persona.cargo && (
                            <p className="text-xs text-gray-600 mt-1">{persona.cargo}</p>
                          )}
                          <div className="flex items-center space-x-4 mt-1">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                              persona.entrevistado
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {persona.entrevistado ? 'Entrevistado' : 'Pendiente'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {persona.total_reuniones_asistidas} reunión{persona.total_reuniones_asistidas !== 1 ? 'es' : ''}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Resumen Rápido</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total reuniones:</span>
                <span className="text-sm font-medium text-gray-900">{estadisticas.total_reuniones}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total entrevistas:</span>
                <span className="text-sm font-medium text-gray-900">{estadisticas.total_entrevistas}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Personas entrevistadas:</span>
                <span className="text-sm font-medium text-gray-900">
                  {estadisticas.total_personas_entrevistadas} de {personas_empresa.length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Cobertura de entrevistas:</span>
                <span className="text-sm font-medium text-gray-900">
                  {estadisticas.porcentaje_personas_entrevistadas}%
                </span>
              </div>
            </div>

            {/* Progress bar */}
            <div className="mt-4">
              <div className="flex justify-between text-xs text-gray-600 mb-1">
                <span>Progreso de entrevistas</span>
                <span>{estadisticas.porcentaje_personas_entrevistadas}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${estadisticas.porcentaje_personas_entrevistadas}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PestanaReunionesEmpresa;
